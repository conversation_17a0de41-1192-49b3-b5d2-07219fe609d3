MCP Server Fetch 安装完成！

✅ 安装状态：
- uv 已安装 (版本 0.6.11)
- mcp-server-fetch 已通过 uvx 成功安装
- MCP 检查器已启动并运行在 http://127.0.0.1:6274

📁 配置文件：
1. claude_desktop_config.json - 基础配置
2. claude_desktop_config_custom.json - 带自定义选项的配置

🔧 使用方法：
1. 将配置文件内容复制到您的 Claude Desktop 配置中
2. 重启 Claude Desktop 应用
3. 现在您可以使用 fetch 工具来抓取网页内容

🛠️ 可用工具：
- fetch: 从互联网抓取 URL 并转换为 markdown
  参数：
  - url (必需): 要抓取的 URL
  - max_length (可选): 最大字符数 (默认: 5000)
  - start_index (可选): 开始提取的字符索引 (默认: 0)
  - raw (可选): 获取原始内容而非 markdown (默认: false)

🔍 调试：
- MCP 检查器正在运行，可以用来测试和调试服务器
- 访问: http://localhost:6274/?MCP_PROXY_AUTH_TOKEN=0fc5850d13229e30ebb9b438f2bd477ab8ca5e0258991df66dd71a26093412e2

⚙️ 自定义选项：
- --ignore-robots-txt: 忽略 robots.txt 限制
- --user-agent=YourAgent: 自定义用户代理
- --proxy-url=URL: 使用代理服务器
